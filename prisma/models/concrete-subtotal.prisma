// 商品混凝土小计-单据表
model ConcreteSubtotal {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  code         String       @map("code") /// 单据编码
  submitStatus SubmitStatus @default(PENDING) @map("submit_status") /// 提交状态
  auditStatus  AuditStatus  @default(PENDING) @map("audit_status") /// 审批状态
  year         Int          @map("year") /// 年
  month        Int          @map("month") /// 月
  day          Int          @map("day") /// 日

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("concrete_subtotal")
}

// // 商品混凝土小计-单据明细表

// model ConcreteSubtotalDetail {
// }

// // 商品混凝土小计-附件表

// model ConcreteSubtotalAttachment {
// }
