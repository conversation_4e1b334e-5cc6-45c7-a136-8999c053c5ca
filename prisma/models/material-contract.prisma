// 拟定状态
enum ProposedStatus {
  OFFICIAL // 正式合同
  PROVISIONAL // 暂存合同
}

// 履约状态
enum FulfillmentStatus {
  NOT_STARTED // 未开始
  IN_PROGRESS // 履约中
  COMPLETED // 已完成
}

// 乙方类型
enum PartyBType {
  COMPANY // 公司
  SUPPLIER // 供应商
}

// 变更类型
enum AlterType {
  ADJUSTMENT_QUANTITY // 调量
  ADJUSTMENT_PRICE // 调价
  ADJUSTMENT_QUANTITY_AND_PRICE // 调量价
  ADD_ITEM // 增项
}

/// 物资合同
model MaterialContract {
  // 主键 & 外键
  id                 String @id @default(uuid(7))
  orgId              String @map("org_id") // 组织id（项目id）
  tenantId           String @map("tenant_id") // 租户id
  contractTemplateId String @map("contract_template_id") /// 合同范本id

  // 业务字段
  name              String /// 合同名称
  code              String /// 合同编号
  partyA            String            @map("party_a") /// 甲方(所有上级组织创建的公司)
  partyAName        String            @map("party_a_name") /// 甲方名称
  partyAEndName     String            @map("party_a_end_name") /// 甲方最终名称
  partyB            String            @map("party_b") /// 乙方（所有上级组织创建的公司及顶级组织创建的供应商）
  partyBName        String            @map("party_b_name") /// 乙方名称
  partyBEndName     String            @map("party_b_end_name") /// 乙方最终名称
  partyBType        PartyBType        @map("party_b_type") /// 乙方类型
  priceType         String?           @map("price_type") /// 价格类型
  amount            Decimal?          @map("amount") @db.Decimal(20, 6) /// 合同金额
  taxRate           Decimal?          @map("tax_rate") @db.Decimal(20, 8) /// 税率
  signDate          DateTime?         @map("sign_date") /// 签订时间
  creator           String            @map("creator") /// 编制人
  proposedStatus    ProposedStatus    @map("proposed_status") /// 拟定状态
  fulfillmentStatus FulfillmentStatus @default(NOT_STARTED) @map("fulfillment_status") /// 履约状态
  submitStatus      SubmitStatus      @default(PENDING) @map("submit_status") /// 提交状态
  auditStatus       AuditStatus       @default(PENDING) @map("audit_status") /// 审核状态
  remark            String? /// 合同说明

  // 树形结构字段
  parentId String? @map("parent_id") /// 父级id
  fullId   String  @default("") @map("full_id") /// 全路径id(使用|分隔)
  fullName String  @default("") @map("full_name") /// 全名称(使用|分隔)
  level    Int     @default(1) @map("level") /// 级别
  sort     Int     @default(1) @map("sort") /// 排序
  isLeaf   Boolean @default(true) @map("is_leaf") /// 是否叶子节点

  // 合同文件
  fileName        String? @map("file_name") /// 文件名称
  fileExt         String? @map("file_ext") /// 文件扩展名
  fileKey         String? @map("file_key") /// 文件key
  fileSize        String? @map("file_size") /// 文件大小
  fileContentType String? @map("file_content_type") /// 文件类型

  children MaterialContract[] @relation("MaterialContractTree")
  parent   MaterialContract?  @relation("MaterialContractTree", fields: [parentId], references: [id])

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  // 关联关系
  contractTemplate                ContractTemplate                  @relation(fields: [contractTemplateId], references: [id]) // 合同范本
  contractConsumeMaterialDetails  ContractConsumeMaterialDetails[] /// 合同清单(消耗材料)
  contractConcreteDetails         ContractConcreteDetails[] /// 合同清单(商品混凝土材料)
  contractTurnoverMaterialDetails ContractTurnoverMaterialDetails[] /// 合同清单(周转材料)
  contractSurchargeDetails        ContractConcreteSurcharge[] /// 合同清单(附加费)
  materialContractFieldRule       MaterialContractFieldRule[] /// 合同范本所属的字段规则关联关系表

  @@unique([tenantId, orgId, id])
  @@map("material_contract")
}

// 合同范本所属的字段规则关联关系表
model MaterialContractFieldRule {
  // 主键 & 外键
  id                          String @id @default(uuid(7))
  orgId                       String @map("org_id") /// 组织id
  tenantId                    String @map("tenant_id") /// 租户id
  materialContractId          String @map("material_contract_id") /// 合同编制id
  contractTemplateId          String @map("contract_template_id") // 合同范本id
  contractTemplateFieldRuleId String @map("contract_template_field_rule_id") // 合同模板字段规则id

  value        String?  @map("value") // 值
  textValue    String?  @map("text_value") // 文本值
  decimalValue Decimal? @map("decimal_value") @db.Decimal(20, 8) // 小数值

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  // 关联关系
  materialContract MaterialContract @relation(fields: [materialContractId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("material_contract_field_rule")
}

// 合同附加费（商品混凝土材料）
model ContractConcreteSurcharge {
  // 主键 & 外键
  id                 String @id @default(uuid(7))
  tenantId           String @map("tenant_id") /// 租户id
  orgId              String @map("org_id") /// 组织id
  materialContractId String @map("material_contract_id") /// 合同编制id

  name                 String   @map("name") /// 名称
  unit                 String   @map("unit") /// 单位
  price                Decimal? @db.Decimal(20, 6) /// 变更前价格
  changePrice          Decimal? @db.Decimal(20, 6) /// 变更后价格
  changeCalculatePrice Decimal? @db.Decimal(20, 6) /// 变更计算价格

  remark String? /// 备注

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  materialContract MaterialContract @relation(fields: [materialContractId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("contract_concrete_surcharge")
}

// 合同清单(消耗材料)
model ContractConsumeMaterialDetails {
  // 主键 & 外键
  id                           String  @id @default(uuid(7))
  tenantId                     String  @map("tenant_id") /// 租户id
  orgId                        String  @map("org_id") /// 组织id
  materialContractId           String  @map("material_contract_id") /// 合同编制id
  materialDictionaryVersionId  String  @map("material_dictionary_version_id") /// 核算材料料字典版本id
  materialDictionaryCategoryId String  @map("material_dictionary_category_id") /// 核酸材料字典分类id
  materialDictionaryDetailId   String  @map("material_dictionary_detail_id") /// 核算材料字典明细id
  sourceMaterialContractId     String? @map("source_material_contract_id") /// 源合同/补充协议id

  // 业务字段
  unit            String? /// 单位
  qualityStandard String? @map("quality_standard") /// 材质、性能参数等（或执行的技术质量标准
  brand           String? @map("brand") /// 品牌或厂家

  // 变更前
  priceExcludingTax        Decimal? @map("price_excluding_tax") @db.Decimal(20, 6) /// 原不含税单价
  addedTaxAmount           Decimal? @map("added_tax_amount") @db.Decimal(20, 8) /// 原增值税额
  priceIncludingTax        Decimal? @map("price_including_tax") @db.Decimal(20, 6) /// 原含税单价
  provisionalQuantity      Decimal? @map("provisional_quantity") @db.Decimal(20, 8) /// 原暂定数量
  settlementQuantity       Decimal? @map("settlement_quantity") @db.Decimal(20, 8) /// 结算数量
  totalPriceExcludingTax   Decimal? @map("total_price_excluding_tax") @db.Decimal(20, 6) /// 变更前不含税总价
  totalValueAddedTaxAmount Decimal? @map("total_value_added_tax_amount") @db.Decimal(20, 8) /// 变更前增值税总额
  totalPriceIncludingTax   Decimal? @map("total_price_including_tax") @db.Decimal(20, 6) /// 变更前含税总价

  // 变更后
  changeQuantity                 Decimal?   @map("change_quantity") @db.Decimal(20, 8) /// 变更数量
  changePriceExcludingTax        Decimal?   @map("change_price_excluding_tax") @db.Decimal(20, 6) /// 变更后不含税单价
  changePriceIncludingTax        Decimal?   @map("change_price_including_tax") @db.Decimal(20, 6) /// 变更后含税单价
  changeAddedTaxAmount           Decimal?   @map("change_added_tax_amount") @db.Decimal(20, 8) /// 变更后增值税额
  alterType                      AlterType? @map("change_type") /// 变更类型
  changeTotalPriceExcludingTax   Decimal?   @map("change_total_price_excluding_tax") @db.Decimal(20, 6) /// 变更后不含税总价
  changeTotalValueAddedTaxAmount Decimal?   @map("change_total_value_added_tax_amount") @db.Decimal(20, 8) /// 变更后增值税总额
  changeTotalPriceIncludingTax   Decimal?   @map("change_total_price_including_tax") @db.Decimal(20, 6) /// 变更后含税总价
  sort                           Int        @default(autoincrement()) /// 排序号
  remark                         String? /// 备注

  // 变更增减金额
  changeCalculateTotalPriceExcludingTax   Decimal? @map("change_calculate_total_price_excluding_tax") @db.Decimal(20, 6) /// 变更增减不含税总价
  changeCalculateTotalValueAddedTaxAmount Decimal? @map("change_calculate_total_value_added_tax_amount") @db.Decimal(20, 8) /// 变更增减增值税总额
  changeCalculateTotalPriceIncludingTax   Decimal? @map("change_calculate_total_price_including_tax") @db.Decimal(20, 6) /// 变更增减含税总价

  // 变更详情
  priceCalculate    Decimal? @map("price_calculate") @db.Decimal(20, 6) /// 变更价格增减
  quantityCalculate Decimal? @map("quantity_calculate") @db.Decimal(20, 8) /// 变更数量增减
  changePercentage  String?  @map("change_percentage") /// 增减百分比

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  materialContract MaterialContract @relation(fields: [materialContractId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("contract_consume_material_details")
}

// 合同清单(商品混凝土材料)
model ContractConcreteDetails {
  // 主键 & 外键
  id                           String  @id @default(uuid(7))
  tenantId                     String  @map("tenant_id") /// 租户id
  orgId                        String  @map("org_id") /// 组织id
  materialContractId           String  @map("material_contract_id") /// 合同编制id
  materialDictionaryVersionId  String  @map("material_dictionary_version_id") /// 核算材料料字典版本id
  materialDictionaryCategoryId String  @map("material_dictionary_category_id") /// 核酸材料字典分类id
  materialDictionaryDetailId   String  @map("material_dictionary_detail_id") /// 核算材料字典明细id
  sourceMaterialContractId     String? @map("source_material_contract_id") /// 源合同/补充协议id

  // 业务字段
  unit String? /// 单位

  // 变更前
  priceExcludingTax        Decimal? @map("price_excluding_tax") @db.Decimal(20, 6) /// 原不含税单价
  addedTaxAmount           Decimal? @map("added_tax_amount") @db.Decimal(20, 8) /// 原增值税额
  priceIncludingTax        Decimal? @map("price_including_tax") @db.Decimal(20, 6) /// 原含税单价
  provisionalQuantity      Decimal? @map("provisional_quantity") @db.Decimal(20, 8) /// 原暂定数量
  settlementQuantity       Decimal? @map("settlement_quantity") @db.Decimal(20, 8) /// 结算数量
  totalPriceExcludingTax   Decimal? @map("total_price_excluding_tax") @db.Decimal(20, 6) /// 变更前不含税总价
  totalValueAddedTaxAmount Decimal? @map("total_value_added_tax_amount") @db.Decimal(20, 8) /// 变更前增值税总额
  totalPriceIncludingTax   Decimal? @map("total_price_including_tax") @db.Decimal(20, 6) /// 变更前含税总价

  // 变更后
  changeQuantity                 Decimal?   @map("change_quantity") @db.Decimal(20, 8) /// 变更数量
  changePriceExcludingTax        Decimal?   @map("change_price_excluding_tax") @db.Decimal(20, 6) /// 变更后不含税单价
  changePriceIncludingTax        Decimal?   @map("change_price_including_tax") @db.Decimal(20, 6) /// 变更后含税单价
  changeAddedTaxAmount           Decimal?   @map("change_added_tax_amount") @db.Decimal(20, 8) /// 变更后增值税额
  alterType                      AlterType? @map("change_type") /// 变更类型
  changeTotalPriceExcludingTax   Decimal?   @map("change_total_price_excluding_tax") @db.Decimal(20, 6) /// 变更后不含税总价
  changeTotalValueAddedTaxAmount Decimal?   @map("change_total_value_added_tax_amount") @db.Decimal(20, 8) /// 变更后增值税总额
  changeTotalPriceIncludingTax   Decimal?   @map("change_total_price_including_tax") @db.Decimal(20, 6) /// 变更后含税总价
  sort                           Int        @default(autoincrement()) /// 排序号
  remark                         String? /// 备注

  // 变更增减金额
  changeCalculateTotalPriceExcludingTax   Decimal? @map("change_calculate_total_price_excluding_tax") @db.Decimal(20, 6) /// 变更增减不含税总价
  changeCalculateTotalValueAddedTaxAmount Decimal? @map("change_calculate_total_value_added_tax_amount") @db.Decimal(20, 8) /// 变更增减增值税总额
  changeCalculateTotalPriceIncludingTax   Decimal? @map("change_calculate_total_price_including_tax") @db.Decimal(20, 6) /// 变更增减含税总价

  // 变更详情
  priceCalculate    Decimal? @map("price_calculate") @db.Decimal(20, 6) /// 变更价格增减
  quantityCalculate Decimal? @map("quantity_calculate") @db.Decimal(20, 8) /// 变更数量增减
  changePercentage  String?  @map("change_percentage") /// 增减百分比

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  materialContract MaterialContract @relation(fields: [materialContractId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("contract_concrete_details")
}

// 合同清单(租赁周转材料)
model ContractTurnoverMaterialDetails {
  // 主键 & 外键
  id                           String  @id @default(uuid(7))
  tenantId                     String  @map("tenant_id") /// 租户id
  orgId                        String  @map("org_id") /// 组织id
  materialContractId           String  @map("material_contract_id") /// 合同编制id
  materialDictionaryVersionId  String  @map("material_dictionary_version_id") /// 核算材料料字典版本id
  materialDictionaryCategoryId String  @map("material_dictionary_category_id") /// 核酸材料字典分类id
  materialDictionaryDetailId   String  @map("material_dictionary_detail_id") /// 核算材料字典明细id
  sourceMaterialContractId     String? @map("source_material_contract_id") /// 源合同/补充协议id
  // 业务字段
  unit                         String? /// 单位

  // 变更前
  provisionalDays     Int?     @map("provisional_days") /// 原暂定天数
  priceExcludingTax   Decimal? @map("price_excluding_tax") @db.Decimal(20, 6) /// 原不含税单价
  addedTaxAmount      Decimal? @map("added_tax_amount") @db.Decimal(20, 8) /// 原增值税额
  priceIncludingTax   Decimal? @map("price_including_tax") @db.Decimal(20, 6) /// 原含税单价
  provisionalQuantity Decimal? @map("provisional_quantity") @db.Decimal(20, 8) /// 原暂定数量

  // 变更后
  changeProvisionalDays   Int?       @map("change_provisional_days") /// 变更后天数
  changeQuantity          Decimal?   @map("change_quantity") @db.Decimal(20, 8) /// 变更数量
  changePriceExcludingTax Decimal?   @map("change_price_excluding_tax") @db.Decimal(20, 6) /// 变更后不含税单价
  changePriceIncludingTax Decimal?   @map("change_price_including_tax") @db.Decimal(20, 6) /// 变更后含税单价
  changeAddedTaxAmount    Decimal?   @map("change_added_tax_amount") @db.Decimal(20, 8) /// 变更后增值税额
  alterType               AlterType? @map("change_type") /// 变更类型

  // 变更详情
  priceCalculate    Decimal? @map("price_calculate") @db.Decimal(20, 6) /// 变更价格增减
  quantityCalculate Decimal? @map("quantity_calculate") @db.Decimal(20, 8) /// 变更数量增减
  changePercentage  String?  @map("change_percentage") /// 增减百分比

  sort   Int     @default(autoincrement()) /// 排序号
  remark String? /// 备注

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  materialContract MaterialContract @relation(fields: [materialContractId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("contract_turnover_material_details")
}

/// 合同 - 单位换算
model MaterialContractUnitCalculation {
  // 主键 & 外键
  tenantId           String @map("tenant_id") /// 租户id
  orgId              String @map("org_id") /// 组织id
  id                 String @id @default(uuid(7)) @map("id") /// 数据id
  materialDetailId   String @map("material_detail_id") /// 物料字典的明细id（材料id）
  materialContractId String @map("material_contract_id") /// 合同id

  // 业务字段
  unit       String  @map("unit") /// 单位
  factor     Decimal @map("factor") @db.Decimal(10, 5) /// 换算系数 
  remark     String? @map("remark") /// 备注
  isOriginal Boolean @default(false) @map("is_original") /// 是否属于原合同
  // sort          Int     @default(1) @map("sort") /// 排序

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  @@unique([tenantId, orgId, id])
  @@map("material_contract_unit_calculation")
}

// 合同 - 附件
model MaterialContractAccessory {
  // 主键 & 外键
  id                 String @id @default(uuid(7)) @map("id") /// 数据id
  tenantId           String @map("tenant_id") /// 租户id
  orgId              String @map("org_id") /// 组织id
  materialContractId String @map("material_contract_id") /// 合同编制id

  // 业务字段
  fileName        String  @map("file_name") /// 文件名称
  fileExt         String  @map("file_ext") /// 文件扩展名
  fileKey         String  @map("file_key") /// 文件key
  fileSize        String  @map("file_size") /// 文件大小
  fileContentType String  @map("file_content_type") /// 文件类型
  remark          String? @map("remark") // 备注

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_contract_accessory")
}
