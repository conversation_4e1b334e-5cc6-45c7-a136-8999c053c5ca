// import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common';
// import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

// import { ReqUser } from '@/common/decorators/req-user.decorator';
// import { IReqUser } from '@/common/interfaces/req-user.interface';

// import {
//   MaterialAllocationFromAttachmentCreateDto,
//   MaterialAllocationFromAttachmentResDto
// } from './material-allocation-from-attachment.dto';
// import { MaterialAllocationFromAttachmentService } from './material-allocation-from-attachment.service';

// @ApiTags('商品混凝土小计/附件')
// @Controller('material-allocation-from-attachment')
// export class MaterialAllocationFromAttachmentController {
//   constructor(
//     private readonly service: MaterialAllocationFromAttachmentService
//   ) {}

//   @ApiOperation({
//     summary: '获取附件列表',
//     description: '获取附件列表'
//   })
//   @ApiResponse({
//     status: 200,
//     description: '获取附件列表成功',
//     type: MaterialAllocationFromAttachmentResDto,
//     isArray: true
//   })
//   @Get('/:materialAllocationFromId')
//   async getList(
//     @Param('materialAllocationFromId') materialAllocationFromId: string,
//     @ReqUser() reqUser: IReqUser
//   ) {
//     return await this.service.getList(materialAllocationFromId, reqUser);
//   }

//   @ApiOperation({
//     summary: '新增附件列表',
//     description: '新增附件列表'
//   })
//   @ApiResponse({
//     status: 200,
//     description: '新增附件列表成功'
//   })
//   @Post()
//   async add(
//     @ReqUser() reqUser: IReqUser,
//     @Body() data: MaterialAllocationFromAttachmentCreateDto
//   ) {
//     return await this.service.add(data, reqUser);
//   }

//   @ApiOperation({
//     summary: '删除附件列表',
//     description: '删除附件列表'
//   })
//   @ApiResponse({
//     status: 200,
//     description: '获取附件列表成功'
//   })
//   @Delete('/:id')
//   async delete(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
//     return await this.service.delete(id, reqUser);
//   }
// }
