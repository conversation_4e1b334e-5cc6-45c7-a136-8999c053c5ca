// import { Injectable } from '@nestjs/common';

// import { IReqUser } from '@/common/interfaces/req-user.interface';
// import { PrismaService } from '@/common/modules/prisma/prisma.service';

// import { MaterialAllocationFromAttachmentCreateDto } from './material-allocation-from-attachment.dto';

// @Injectable()
// export class MaterialAllocationFromAttachmentService {
//   constructor(private readonly prisma: PrismaService) {}

//   async getList(materialAllocationFromId: string, reqUser: IReqUser) {
//     return await this.prisma.materialAllocationFromAttachment.findMany({
//       where: {
//         materialAllocationFromId,
//         tenantId: reqUser.tenantId,
//         orgId: reqUser.orgId,
//         isDeleted: false
//       }
//     });
//   }

//   async add(
//     data: MaterialAllocationFromAttachmentCreateDto,
//     reqUser: IReqUser
//   ) {
//     return await this.prisma.materialAllocationFromAttachment.create({
//       data: {
//         ...data,
//         tenantId: reqUser.tenantId,
//         orgId: reqUser.orgId,
//         createBy: reqUser.id
//       }
//     });
//   }

//   async delete(id: string, reqUser: IReqUser) {
//     return await this.prisma.materialAllocationFromAttachment.update({
//       where: {
//         id,
//         isDeleted: false
//       },
//       data: {
//         isDeleted: true,
//         updateBy: reqUser.id
//       }
//     });
//   }
// }
