import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';

import { FileOperateDto } from '@/common/dtos/common.dto';
import {
  AuditStatus,
  MaterialSettlementStatus,
  MaterialType,
  SubmitStatus
} from '@/prisma/generated';

export class MaterialRequisitionFormBaseDto {
  @ApiProperty({ description: 'ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiPropertyOptional({ description: '结算状态' })
  @IsOptional()
  @IsEnum(MaterialSettlementStatus)
  settlementStatus?: MaterialSettlementStatus =
    MaterialSettlementStatus.UN_SETTLED;

  @ApiProperty({ description: '单据编码' })
  @IsString()
  code: string;

  @ApiPropertyOptional({ description: '供应商/领料单位ID' })
  @IsOptional()
  @IsString()
  supplierId?: string;

  @ApiPropertyOptional({ description: '使用部位名称' })
  @IsOptional()
  @IsString()
  partName?: string;

  @ApiProperty({ description: '年' })
  @IsInt()
  year: number;

  @ApiProperty({ description: '月' })
  @IsInt()
  month: number;

  @ApiProperty({ description: '日' })
  @IsInt()
  day: number;

  @ApiProperty({ description: '创建人名称' })
  @IsString()
  creator: string;

  @ApiPropertyOptional({ description: '提交状态' })
  @IsOptional()
  @IsEnum(SubmitStatus)
  submitStatus?: SubmitStatus = SubmitStatus.PENDING;

  @ApiPropertyOptional({ description: '审批状态' })
  @IsOptional()
  @IsEnum(AuditStatus)
  auditStatus?: AuditStatus = AuditStatus.PENDING;

  @ApiPropertyOptional({ description: '创建时间' })
  @IsDate({ message: 'createAt 必须是有效日期' })
  @Type(() => Date)
  @IsOptional()
  createAt?: Date;

  @ApiPropertyOptional({ description: '更新时间' })
  @IsDate({ message: 'updateAt 必须是有效日期' })
  @Type(() => Date)
  @IsOptional()
  updateAt?: Date;

  @ApiPropertyOptional({ description: '领料单位名称' })
  @IsOptional()
  @IsString()
  departmentName?: string;
}

export class QueryRequisitionBillListDto {
  @ApiPropertyOptional({ description: '年' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  year?: number;

  @ApiPropertyOptional({ description: '月' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  month?: number;

  @ApiPropertyOptional({ description: '日' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  day?: number;

  @ApiProperty({ description: '是否仅查看自己的数据,默认为false' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  onlyViewSelf: boolean;
}

export class RequisitionBillListResponseDto extends PickType(
  MaterialRequisitionFormBaseDto,
  [
    'id',
    'settlementStatus',
    'code',
    'supplierId',
    'partName',
    'creator',
    'year',
    'month',
    'day',
    'auditStatus',
    'submitStatus',
    'createAt',
    'updateAt'
  ]
) {
  @ApiProperty({ description: '领料单位名称(列表)' })
  departmentName?: string;

  @ApiProperty({ description: '领料单位名称(详情)' })
  oldDepartmentName?: string;

  @ApiProperty({ description: '材料二级类别名称' })
  materialCategories?: string;

  @ApiProperty({ description: '金额' })
  amount?: number;
}

export class RequisitionDepartmentListResponseDto {
  @ApiProperty({ description: '领料单位ID' })
  id: string;
  @ApiProperty({ description: '领料单位名称' })
  name: string;
}

export class UpdateRequisitionBillDto extends PickType(
  MaterialRequisitionFormBaseDto,
  [
    'id',
    'supplierId',
    'submitStatus',
    'auditStatus',
    'partName',
    'year',
    'month',
    'day',
    'departmentName'
  ]
) {}

export class MaterialCategoryListResponseDto {
  @ApiProperty({ description: 'ID' })
  id: string;

  @ApiProperty({ description: '父ID' })
  parentId: string;

  @ApiProperty({ description: '类别名称' })
  name: string;

  @ApiProperty({ description: '编码' })
  code: string;

  @ApiProperty({ description: '材料类型' })
  @IsEnum(MaterialType, { message: '材料类型必须是MaterialType 枚举值' })
  type: MaterialType;

  @ApiProperty({ description: '层级' })
  level: string;

  @ApiProperty({ description: '备注' })
  remark: string;
}

export class MaterialDetailListResponseDto {
  @ApiProperty({ description: 'ID' })
  id: string;

  @ApiProperty({ description: '合同材料名称' })
  materialName: string;

  @ApiProperty({ description: '规格型号' })
  materialSpec: string;

  @ApiProperty({ description: '计量单位' })
  unit: string;

  @ApiProperty({ description: '核算类型' })
  type: string;

  @ApiProperty({ description: '备注' })
  remark: string;
}

export class RequisitionFormDetailBaseDto {
  @ApiProperty({ description: 'ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ description: '领料单ID' })
  @IsString()
  requisitionFormId: string;

  @ApiProperty({ description: '材料ID' })
  @IsString()
  materialId: string;

  @ApiPropertyOptional({ description: '材料名称' })
  @IsOptional()
  @IsString()
  materialName?: string;

  @ApiPropertyOptional({ description: '材料规格' })
  @IsOptional()
  @IsString()
  materialSpec?: string;

  @ApiProperty({ description: '计量单位' })
  @IsString()
  unit: string;

  @ApiPropertyOptional({ description: '库存数量', type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  inventoryQuantity?: number;

  @ApiPropertyOptional({ description: '实发数量', type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  actualQuantity?: number;

  @ApiPropertyOptional({ description: '领料单价', type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  price?: number;

  @ApiPropertyOptional({ description: '领料金额', type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  amount?: number;

  @ApiPropertyOptional({ description: '排序号', type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  orderNo?: number;

  @ApiPropertyOptional({ description: '业务成本科目明细ID', type: String })
  @IsOptional()
  @IsString()
  businessCostSubjectDetailId?: string;
}

export class CreateRequisitionDetailDto extends PickType(
  RequisitionFormDetailBaseDto,
  [
    'materialId',
    'materialName',
    'materialSpec',
    'unit',
    'inventoryQuantity',
    'actualQuantity'
  ]
) {}

export class UpdateRequisitionDetailDto extends PickType(
  RequisitionFormDetailBaseDto,
  ['id', 'actualQuantity', 'businessCostSubjectDetailId']
) {}

export class CreateRequisitionAttachmentDto extends PickType(FileOperateDto, [
  'fileName',
  'fileKey',
  'fileSize',
  'fileExt',
  'fileContentType'
]) {
  @ApiProperty({ description: '领料单ID' })
  @IsString()
  @IsNotEmpty()
  requisitionFormId: string;
}

export class RequisitionAttachmentResponseDto extends PickType(FileOperateDto, [
  'fileName',
  'fileKey',
  'fileSize',
  'fileExt',
  'fileContentType'
]) {
  @ApiProperty({ description: '数据ID' })
  @IsString()
  id: string;
}

export class BusinessCostSubjectDetailResponseDto {
  @ApiProperty({ description: '材料ID' })
  materialId: string;

  @ApiProperty({ description: '成本科目ID' })
  businessCostSubjectDetailId: string;

  @ApiProperty({ description: '成本科目名称' })
  name: string;
}
