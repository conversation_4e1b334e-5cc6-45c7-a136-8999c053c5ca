import { add, multiply, subtract } from '@ewing/infra-cloud-sdk';
import { BadRequestException, Injectable } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { v7 as uuidv7 } from 'uuid';

import { CommonRepositories } from '@/common/common-repositories';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PrismaClient } from '@/prisma/generated';

import {
  CreateRequisitionDetailDto,
  MaterialCategoryListResponseDto,
  MaterialDetailListResponseDto,
  UpdateRequisitionDetailDto
} from '../material-requisition-form.dto';
import {
  SelectMaterialReceiveInfo,
  SelectReturnInventoryInfo,
  UpdateReceiveDetail,
  UpdateReversalDetail
} from '../material-requisition-form.interface';
import { RequisitionFormRepository } from '../repositories/requisition-form.repository';
import { RequisitionFormDetailRepository } from '../repositories/requisition-form-detail.repository';

@Injectable()
export class MaterialRequisitionFormDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: RequisitionFormDetailRepository,
    private readonly billRepository: RequisitionFormRepository
  ) {}

  // 获取领料单明细
  async getRequisitionDetailList(reqUser: IReqUser, requisitionBillId: string) {
    const result = await this.prisma.materialRequisitionFormDetail.findMany({
      where: {
        requisitionFormId: requisitionBillId,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      orderBy: {
        orderNo: 'asc'
      }
    });

    return result;
  }

  // 获取可选择的材料字典分类
  async getMaterialCategoryList(
    reqUser: IReqUser,
    requisitionBillId: string
  ): Promise<MaterialCategoryListResponseDto[]> {
    const requisitionBill = await this.prisma.materialRequisitionForm.findFirst(
      {
        where: {
          id: requisitionBillId,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        select: {
          year: true,
          month: true,
          day: true
        }
      }
    );
    if (!requisitionBill) return [];

    const { year, month, day } = requisitionBill;

    const result = await this.repository.selectMaterialCategoryList(
      reqUser,
      year,
      month,
      day
    );

    return result;
  }

  // 获取可选择的材料明细
  async getMaterialDetailList(
    reqUser: IReqUser,
    requisitionBillId: string,
    categoryId: string
  ): Promise<MaterialDetailListResponseDto[]> {
    const requisitionBill = await this.prisma.materialRequisitionForm.findFirst(
      {
        where: {
          id: requisitionBillId,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        select: {
          year: true,
          month: true,
          day: true
        }
      }
    );
    if (!requisitionBill) return [];

    const { year, month, day } = requisitionBill;
    const result = await this.repository.selectMaterialDetailList(
      reqUser,
      categoryId,
      year,
      month,
      day
    );

    // 查询业务成本科目
    const materialIds = result.map((item) => item.id) || [];
    const businessCostSubjectDetails =
      await this.billRepository.selectBusinessCostSubjectList(
        reqUser.tenantId,
        reqUser.orgId,
        requisitionBillId,
        materialIds
      );

    return result;
  }

  // 新增领料单明细
  async addRequisitionDetails(
    reqUser: IReqUser,
    requisitionBillId: string,
    data: CreateRequisitionDetailDto[]
  ) {
    const { tenantId, orgId } = reqUser;

    // 查询收料单据
    const requisitionBillInfo =
      await this.prisma.materialRequisitionForm.findFirst({
        where: {
          id: requisitionBillId,
          isDeleted: false,
          tenantId,
          orgId
        },
        select: { year: true, month: true, day: true }
      });
    if (!requisitionBillInfo) throw new BadRequestException('领料单不存在!');

    // 1. 获取领料单和退库单信息
    const { materialReceivingMap, reversalBillMap } =
      await this.selectReceivingAndReturnInventoryInfo(
        reqUser,
        requisitionBillInfo
      );

    // 2. 生成领料单明细入库的数据
    const {
      createRequisitionDetail,
      updateReceiveDetail,
      updateReversalDetail
    } = this.processAddRequisitionDetailData(
      reqUser,
      requisitionBillId,
      data,
      materialReceivingMap,
      reversalBillMap
    );

    // 3. 数据入库
    await this.addRequisitionDetailSaveTable(
      reqUser,
      requisitionBillId,
      createRequisitionDetail,
      updateReceiveDetail,
      updateReversalDetail
    );

    return true;
  }

  // 新增领料单明细-获取领料单和退库单信息
  async selectReceivingAndReturnInventoryInfo(
    reqUser: IReqUser,
    requisitionBillInfo: any
  ): Promise<{
    materialReceivingMap: Record<string, SelectMaterialReceiveInfo[]>;
    reversalBillMap: Record<string, SelectReturnInventoryInfo[]>;
  }> {
    const { tenantId, orgId } = reqUser;
    const { year, month, day } = requisitionBillInfo;
    const receiveBill: SelectMaterialReceiveInfo[] =
      await this.repository.selectMaterialReceiveInfo(
        tenantId,
        orgId,
        year,
        month,
        day
      );
    const materialReceivingIds = []; // 库存包含退库来的收料单ids
    for (const item of receiveBill) {
      materialReceivingIds.push(item.id);
    }
    const materialReceivingMap = receiveBill.reduce(
      (acc, cur) => {
        if (!acc[cur.materialId]) {
          acc[cur.materialId] = [];
        }
        acc[cur.materialId].push(cur);
        return acc;
      },
      {} as Record<string, SelectMaterialReceiveInfo[]>
    );

    // 查询退库单数据
    let reversalBill: SelectReturnInventoryInfo[] = [];
    if (!isEmpty(materialReceivingIds)) {
      reversalBill = await this.repository.selectReturnInventoryInfo(
        tenantId,
        orgId,
        materialReceivingIds,
        year,
        month,
        day
      );
    }
    const reversalBillMap = reversalBill.reduce(
      (acc, cur) => {
        const key = `${cur.materialReceivingId}@${cur.materialId}`;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(cur);
        return acc;
      },
      {} as Record<string, SelectReturnInventoryInfo[]>
    );

    return { materialReceivingMap, reversalBillMap };
  }

  // 新增领料单明细-生成领料单明细入库的数据
  processAddRequisitionDetailData(
    reqUser: IReqUser,
    requisitionBillId: string,
    data: CreateRequisitionDetailDto[],
    materialReceivingMap: Record<string, SelectMaterialReceiveInfo[]>,
    reversalBillMap: Record<string, SelectReturnInventoryInfo[]>,
    generateParent: boolean = true,
    parentId?: string
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 处理领料单入库数据和收料单、退库单更新数据
    const createRequisitionDetail: any[] = [];
    const updateReceiveDetail: UpdateReceiveDetail[] = [];
    const updateReversalDetail: UpdateReversalDetail[] = [];
    for (const item of data) {
      const id = generateParent ? uuidv7() : parentId;

      let tempActualQuantity: any = item.actualQuantity || 0; // 领料数量
      // 从领料单里找
      const receivingBillList = materialReceivingMap[item.materialId] || [];
      for (const receivingBill of receivingBillList) {
        if (tempActualQuantity <= 0) break;

        // 如果有退库单，优先使用退库单的库存
        const reversalBillList =
          reversalBillMap[`${receivingBill.id}@${receivingBill.materialId}`] ||
          [];
        for (const reversalBill of reversalBillList) {
          if (tempActualQuantity <= 0) break;

          let actualQuantity;
          if (reversalBill.inventoryQuantity >= tempActualQuantity) {
            updateReversalDetail.push({
              id: reversalBill.detailId,
              reversalInventoryQuantity: +subtract(
                reversalBill.inventoryQuantity,
                tempActualQuantity
              ).toFixed(8)
            });
            actualQuantity = tempActualQuantity;
            tempActualQuantity = 0;
          } else {
            updateReversalDetail.push({
              id: reversalBill.detailId,
              reversalInventoryQuantity: 0
            });
            actualQuantity = reversalBill.inventoryQuantity;
            tempActualQuantity = subtract(
              tempActualQuantity,
              reversalBill.inventoryQuantity
            ).toFixed(8);
          }

          createRequisitionDetail.push({
            parentId: id,
            requisitionFormId: requisitionBillId,
            materialId: item.materialId,
            materialName: reversalBill.code,
            materialSpec: reversalBill.returnInventoryDate,
            materialReceivingId: reversalBill.id,
            materialReceivingInventoryId: reversalBill.detailId,
            unit: item.unit,
            inventoryQuantity: item.inventoryQuantity,
            actualQuantity,
            actualInventoryQuantity: actualQuantity,
            price: reversalBill.price,
            amount: multiply(actualQuantity, reversalBill.price).toFixed(8),
            tenantId,
            orgId,
            createBy: userId,
            updateBy: userId
          });
        }
        if (tempActualQuantity <= 0) break;

        // 如果没有退库单，或者退库单的库存不足，再使用收料单的库存
        let actualQuantity;
        if (receivingBill.inventoryQuantity >= tempActualQuantity) {
          updateReceiveDetail.push({
            id: receivingBill.detailId,
            inventoryQuantity: +subtract(
              receivingBill.inventoryQuantity,
              tempActualQuantity
            ).toFixed(8),
            requisitionQuantity: +add(
              receivingBill.requisitionQuantity,
              tempActualQuantity
            ).toFixed(8)
          });
          actualQuantity = tempActualQuantity;
          tempActualQuantity = 0;
        } else {
          updateReceiveDetail.push({
            id: receivingBill.detailId,
            inventoryQuantity: 0,
            requisitionQuantity: +add(
              receivingBill.requisitionQuantity,
              receivingBill.inventoryQuantity
            ).toFixed(8)
          });
          actualQuantity = receivingBill.inventoryQuantity;
          tempActualQuantity = subtract(
            tempActualQuantity,
            receivingBill.inventoryQuantity
          ).toFixed(8);
        }

        createRequisitionDetail.push({
          parentId: id,
          requisitionFormId: requisitionBillId,
          materialId: item.materialId,
          materialName: receivingBill.code,
          materialSpec: receivingBill.receivingDate,
          materialReceivingId: receivingBill.id,
          materialReceivingInventoryId: receivingBill.detailId,
          unit: item.unit,
          inventoryQuantity: item.inventoryQuantity,
          actualQuantity,
          actualInventoryQuantity: actualQuantity,
          price: receivingBill.price,
          amount: multiply(actualQuantity, receivingBill.price).toFixed(8),
          tenantId,
          orgId,
          createBy: userId,
          updateBy: userId
        });
      }

      // 添加领料单明细
      if (generateParent) {
        createRequisitionDetail.push({
          ...item,
          requisitionFormId: requisitionBillId,
          id,
          tenantId,
          orgId,
          createBy: userId,
          updateBy: userId
        });
      }
    }

    return {
      createRequisitionDetail,
      updateReceiveDetail,
      updateReversalDetail
    };
  }

  // 新增领料单明细，数据入库
  async addRequisitionDetailSaveTable(
    reqUser: IReqUser,
    requisitionBillId: string,
    createRequisitionDetail: any[],
    updateReceiveDetail: UpdateReceiveDetail[],
    updateReversalDetail: UpdateReversalDetail[]
  ) {
    await this.prisma.$transaction(async (tx) => {
      // 创建领料单明细
      await tx.materialRequisitionFormDetail.createMany({
        data: createRequisitionDetail
      });

      if (!isEmpty(updateReceiveDetail)) {
        // 更新收料单
        await this.repository.bulkUpdateMaterialReceiveDetail(
          reqUser,
          tx as PrismaService,
          updateReceiveDetail
        );
      }

      if (!isEmpty(updateReversalDetail)) {
        // 更新退库单
        await this.repository.bulkUpdateMaterialReturnInventoryDetail(
          reqUser,
          tx as PrismaService,
          updateReversalDetail
        );
      }

      // 更新父级的单价和金额
      await this.repository.updateParentDetailPriceAndAmount(
        reqUser,
        tx as PrismaService,
        requisitionBillId
      );
    });
  }

  // 删除领料单明细
  async deleteRequisitionDetail(reqUser: IReqUser, id: string) {
    const { tenantId, orgId, id: userId } = reqUser;

    await this.prisma.$transaction(async (tx) => {
      // 删除领料单明细，需要返还收料单和退库单的库存
      await this.repository.updateMaterialReceivingDetailInventory(
        tx as PrismaClient,
        reqUser,
        id
      );
      await this.repository.updateMaterialReversalDetailInventory(
        tx as PrismaClient,
        reqUser,
        id
      );

      // 删除父级（材料）
      await tx.materialRequisitionFormDetail.update({
        data: {
          isDeleted: true,
          updateBy: userId
        },
        where: {
          id,
          isDeleted: false,
          tenantId: tenantId,
          orgId: orgId
        }
      });
      // 删除子明细
      await tx.materialRequisitionFormDetail.updateMany({
        data: {
          isDeleted: true,
          updateBy: userId
        },
        where: {
          parentId: id,
          isDeleted: false,
          tenantId: tenantId,
          orgId: orgId
        }
      });
    });

    return true;
  }

  // 编辑领料单明细
  async editRequisitionDetail(
    reqUser: IReqUser,
    data: UpdateRequisitionDetailDto
  ) {
    const oldDetailInfo =
      await this.prisma.materialRequisitionFormDetail.findFirst({
        where: {
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          id: data.id
        },
        select: {
          requisitionFormId: true,
          actualQuantity: true,
          businessCostSubjectDetailId: true,
          materialId: true,
          materialName: true,
          materialSpec: true,
          unit: true,
          price: true,
          inventoryQuantity: true
        }
      });

    if (!oldDetailInfo) throw new BadRequestException('领料单明细数据不存在');

    // 是否改变了实收数量
    const isChangeActualQuantity =
      +(data.actualQuantity || 0) !== +(oldDetailInfo.actualQuantity || 0);

    // 先删除数据，在重新分配库存，用两个事务
    await this.prisma.$transaction(async (tx) => {
      await tx.materialRequisitionFormDetail.update({
        where: {
          id: data.id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        data: { ...data, updateBy: reqUser.id }
      });

      if (isChangeActualQuantity) {
        // 删除领料单明细，需要返还收料单和退库单的库存
        await this.repository.updateMaterialReceivingDetailInventory(
          tx as PrismaClient,
          reqUser,
          data.id
        );
        await this.repository.updateMaterialReversalDetailInventory(
          tx as PrismaClient,
          reqUser,
          data.id
        );
        // 删除旧的子级明细
        await tx.materialRequisitionFormDetail.updateMany({
          where: {
            parentId: data.id,
            isDeleted: false,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId
          },
          data: { isDeleted: true, updateBy: reqUser.id }
        });
      }
    });

    if (!isChangeActualQuantity) return true;

    // 新增子级明细，按新的实收数量重新分配
    const requisitionBillInfo =
      await this.prisma.materialRequisitionForm.findFirst({
        where: {
          id: oldDetailInfo.requisitionFormId,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        select: { year: true, month: true, day: true }
      });

    // 1. 获取领料单和退库单信息
    const { materialReceivingMap, reversalBillMap } =
      await this.selectReceivingAndReturnInventoryInfo(
        reqUser,
        requisitionBillInfo
      );

    // 2. 生成领料单明细入库的数据
    const detailData: CreateRequisitionDetailDto[] = [
      {
        materialId: oldDetailInfo.materialId,
        materialName: oldDetailInfo.materialName || '',
        materialSpec: oldDetailInfo.materialSpec || '',
        unit: oldDetailInfo.unit,
        inventoryQuantity: +(oldDetailInfo.inventoryQuantity || 0),
        actualQuantity: data.actualQuantity
      }
    ];
    const {
      createRequisitionDetail,
      updateReceiveDetail,
      updateReversalDetail
    } = this.processAddRequisitionDetailData(
      reqUser,
      oldDetailInfo.requisitionFormId,
      detailData,
      materialReceivingMap,
      reversalBillMap,
      false,
      data.id
    );

    // 3.数据入库
    await this.prisma.$transaction(async (tx) => {
      // 新增子级明细
      await tx.materialRequisitionFormDetail.createMany({
        data: createRequisitionDetail
      });

      // 处理收料单和退库单库存
      if (!isEmpty(updateReceiveDetail)) {
        await this.repository.bulkUpdateMaterialReceiveDetail(
          reqUser,
          tx as PrismaClient,
          updateReceiveDetail
        );
      }
      if (!isEmpty(updateReversalDetail)) {
        await this.repository.bulkUpdateMaterialReturnInventoryDetail(
          reqUser,
          tx as PrismaClient,
          updateReversalDetail
        );
      }

      // 更新父级的单价和金额
      await this.repository.updateParentDetailPriceAndAmount(
        reqUser,
        tx as PrismaService,
        oldDetailInfo.requisitionFormId
      );
    });

    return true;
  }

  // 领料单明细上移下移
  async moveRequisitionDetail(reqUser: IReqUser, fromId: string, toId: string) {
    await CommonRepositories.changeDataOrderNo(this.prisma, {
      tenantId: reqUser.tenantId,
      orgId: reqUser.orgId,
      fromId,
      toId,
      tableName: 'material_requisition_form_detail'
    });
    return true;
  }
}
