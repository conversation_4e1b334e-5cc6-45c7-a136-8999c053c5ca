import { Injectable } from '@nestjs/common';
import { isEmpty } from 'lodash';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Prisma, SupplierDirectoryClassify } from '@/prisma/generated';

import { QueryRequisitionBillListDto } from '../material-requisition-form.dto';

@Injectable()
export class RequisitionFormRepository {
  constructor(private readonly prisma: PrismaService) {}

  async selectBillList(reqUser: IReqUser, query: QueryRequisitionBillListDto) {
    const { onlyViewSelf = false } = query;

    const result = await this.prisma.$queryRaw<any[]>`
      with temp_material_categories as (
        select
          mrfd.requisition_form_id,
          STRING_AGG(distinct
            case
              when position('|' in mdc.full_name) > 0
              then split_part(mdc.full_name, '|', 2)
              else split_part(mdc.full_name, '|', 1)
            end,
            ','
          ) filter (where mdc.full_name is not null) material_categories
        from material_requisition_form_detail mrfd
        join material_dictionary_detail mdd
          on mdd.id = mrfd.material_id
          and mdd.is_deleted = false
          and mdd.tenant_id = ${reqUser.tenantId}
        join material_dictionary_category mdc
          on mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.org_id = mdd.org_id
        where mrfd.is_deleted = false
          and mrfd.tenant_id = ${reqUser.tenantId}
          and mrfd.org_id = ${reqUser.orgId}
        group by mrfd.requisition_form_id
      )
      , temp_bill_amount as (
        select
          mrfd.requisition_form_id
          ,round(sum(round(mrfd.amount,2)),2) as bill_amount
        from material_requisition_form_detail mrfd
        where mrfd.is_deleted = false
          and mrfd.tenant_id = ${reqUser.tenantId}
          and mrfd.org_id = ${reqUser.orgId}
          and mrfd.parent_id is null
        group by mrfd.requisition_form_id
      )
      select
        mrf.settlement_status
        ,mrf.id
        ,mrf.code
        ,mrf.part_name
        ,mrf.supplier_id
        ,mrf.department_name as old_department_name
        ,tmc.material_categories
        ,temp_bill_amount.bill_amount as amount
        ,mrf.creator
        ,mrf.year
        ,mrf.month
        ,mrf.day
        ,mrf.submit_status
        ,mrf.audit_status
        ,mrf.excel_file_key
        ,mrf.qr_code_url
        ,mrf.excel_file_key_a4
        ,mrf.qr_code_url_a4
      from material_requisition_form mrf
      left join temp_bill_amount
        on temp_bill_amount.requisition_form_id = mrf.id
      left join temp_material_categories tmc
        on tmc.requisition_form_id = mrf.id
      where mrf.tenant_id = ${reqUser.tenantId}
        and mrf.org_id = ${reqUser.orgId}
        and mrf.is_deleted = false
        ${onlyViewSelf ? Prisma.sql`and mrf.create_by = ${reqUser.id}` : Prisma.empty}
        ${query.year ? Prisma.sql`and mrf.year = ${query.year}` : Prisma.empty}
        ${query.month ? Prisma.sql`and mrf.month = ${query.month}` : Prisma.empty}
        ${query.day ? Prisma.sql`and mrf.day = ${query.day}` : Prisma.empty}
      order by mrf.code desc, mrf.id desc
    `;

    return result;
  }

  // 获取供应商名录
  async selectSupplierList(tenantId: string, orgIds: string[]) {
    const suppliers = await this.prisma.$queryRaw`
      select
        id
        ,full_name as name
      from supplier_directory
      where is_deleted = false
        and tenant_id = ${tenantId}
        and org_id in (${Prisma.join(orgIds)})
        and classify <> ARRAY[${SupplierDirectoryClassify.MATERIAL_PURCHASING}]::"SupplierDirectoryClassify"[]
    `;

    return suppliers;
  }

  // 获取领料单下选择材料的业务成本科目信息
  async selectBusinessCostSubjectList(
    tenantId: string,
    orgId: string,
    requisitionBillId: string,
    materialIds: string[]
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
      with temp_materials as (
        select distinct mrfd.material_id
        from material_requisition_form_detail mrfd
        where mrfd.is_deleted = false
          and mrfd.tenant_id = ${tenantId}
          and mrfd.org_id = ${orgId}
          and mrfd.requisition_form_id = ${requisitionBillId}
      )
      select
        mdd.id as material_id
        ,bsd.id as business_cost_subject_detail_id
        ,bsd.name
      from material_dictionary_detail mdd
      join account_material_detail_business_cost_subject_detail mdd_ref
        on mdd_ref.is_deleted = false
        and mdd_ref.tenant_id = mdd.tenant_id
        and mdd_ref.material_dictionary_detail_id = mdd.id
        and mdd_ref.version_id = mdd.material_dictionary_version_id
      join business_cost_subject_detail bsd
        on bsd.is_deleted = false
        and bsd.tenant_id = mdd_ref.tenant_id
        and bsd.id = mdd_ref.business_cost_subject_detail_id
        and bsd.business_cost_subject_version_id in (
          select version_id
          from account_business_cost_subject_version
          where is_deleted = false
            and tenant_id = ${tenantId}
            and org_id = ${orgId}
        )
      where mdd.is_deleted = false
        and mdd.tenant_id = ${tenantId}
        and mdd.material_dictionary_version_id in (
          select version_id
          from account_material_dictionary_version
          where is_deleted = false
            and tenant_id = ${tenantId}
            and org_id = ${orgId}
        )
        ${
          isEmpty(materialIds)
            ? `and mdd.id in (select material_id from temp_materials)`
            : `and mdd.id in (${Prisma.join(materialIds)})`
        }
    `;

    return result;
  }
}
